<template>
  <div class="resign-container">
    <!-- 搜索条件 -->
    <div class="search-container">
      <el-form :model="conditions" inline class="search-form">
        <div class="search-header">
          <div class="search-main">
            <el-form-item>
              <el-input
                v-model="conditions.filters.name"
                placeholder="请输入姓名"
                clearable
                @keyup.enter.native="onSearch"
              />
            </el-form-item>
            <el-form-item>
              <el-input
                v-model="conditions.filters.cellPhone"
                placeholder="请输入手机号"
                clearable
                @keyup.enter.native="onSearch"
              />
            </el-form-item>
            <el-form-item>
              <el-input
                v-model="conditions.filters.idCard"
                placeholder="请输入身份证号"
                clearable
                @keyup.enter.native="onSearch"
              />
            </el-form-item>
          </div>
          <div class="search-actions">
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="onReset">重置</el-button>
          </div>
        </div>
        <div class="search-filters">
          <el-form-item label="客户:">
            <supplier-customers-selector
              v-model="conditions.filters.customerId"
              style="width: 200px"
              @change="handleCustomerChange"
            />
          </el-form-item>
          <el-form-item label="服务合同:">
            <service-contracts-selector
              v-model="conditions.filters.contractId"
              :customer-id="conditions.filters.customerId"
              :corporation-id="selectedCorporationId"
              style="width: 200px"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <el-table
        :data="data"
        v-loading="loading"
        :height="tableHeight"
        :stripe="false"
        highlight-current-row
        :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
        row-key="id"
      >
        <template slot="empty">
          <div class="empty-data">暂无数据</div>
        </template>
        <el-table-column prop="name" label="姓名" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="cellPhone" label="手机号" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.cellPhone || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="idCard" label="身份证号" min-width="180" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.idCard || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="empStatus" label="员工状态" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.empStatus || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="contractName" label="服务合同" min-width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.contractName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="supplierCorporationName" label="作业主体" min-width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.supplierCorporationName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="entryDate" label="入职日期" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ formatDate(scope.row.entryDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="leaveDate" label="离职日期" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ formatDate(scope.row.leaveDate) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <div class="action-buttons">
              <el-button
                type="text"
                size="small"
                @click="handleConfirmResign(scope.row)"
              >
                确认离职
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleDeleteResign(scope.row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="conditions.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import SupplierCustomersSelector from './selector/supplierCustomers.vue'
import ServiceContractsSelector from './selector/serviceContracts.vue'

const client = makeClient()

export default {
  components: {
    SupplierCustomersSelector,
    ServiceContractsSelector
  },
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 10,
        sorts: [],
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          name: null,
          cellPhone: null,
          idCard: null,
          corporationId: null,
          customerId: null,
          contractId: null,
          empStatus: null,
          contractIds: []
        }
      },
      data: [],
      total: 0,
      loading: false,
      tableHeight: 400,
      selectedCorporationId: null
    }
  },
  async mounted() {
    await this.loadUserProfile()
    this.getList()
    this.setTableHeight()
    window.addEventListener('resize', this.setTableHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setTableHeight)
  },
  methods: {
    async loadUserProfile() {
      try {
        const [err, response] = await client.supplierProfile()
        if (err) {
          handleError(err)
          return
        }

        // 获取当前用户的作业主体信息
        if (response.data && response.data.corporations && response.data.corporations.length > 0) {
          this.selectedCorporationId = response.data.corporations[0].id
        }
      } catch (error) {
        console.error('Failed to load user profile:', error)
      }
    },

    setTableHeight() {
      this.$nextTick(() => {
        const windowHeight = window.innerHeight
        const headerHeight = 60
        const searchHeight = 120
        const paginationHeight = 60
        const padding = 40
        this.tableHeight = windowHeight - headerHeight - searchHeight - paginationHeight - padding
      })
    },

    async getList() {
      this.loading = true

      const [err, r] = await client.supplierResignList({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
      this.$nextTick(() => {
        this.setTableHeight()
      })
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0
      this.getList()
    },

    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.conditions = {
        offset: 0,
        limit: 10,
        sorts: [],
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          name: null,
          cellPhone: '',
          idCard: '',
          corporationId: 0,
          customerId: 0,
          contractId: 0,
          empStatus: '',
          contractIds: []
        }
      }
      this.selectedCorporationId = null
      this.$nextTick(() => {
        this.getList()
      })
    },

    handleCustomerChange(customerId) {
      // 当客户改变时，清空服务合同选择
      this.conditions.filters.contractId = 0
    },

    formatDate(dateStr) {
      if (!dateStr) return '-'
      return new Date(dateStr).toLocaleDateString('zh-CN')
    },

    handleConfirmResign(row) {
      this.$confirm('确定要确认该人员离职吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const [err] = await client.supplierResignConfirm({
          body: { id: row.id }
        })

        if (err) {
          handleError(err)
          return
        }

        this.$message.success('确认离职成功')
        this.getList()
      }).catch(() => {
        // 用户取消操作
      })
    },

    handleDeleteResign(row) {
      this.$confirm('确定要删除该离职记录吗？删除后无法恢复。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const [err] = await client.supplierDeleteResign({
          body: { id: row.id }
        })

        if (err) {
          handleError(err)
          return
        }

        this.$message.success('删除成功')
        this.getList()
      }).catch(() => {
        // 用户取消操作
      })
    }
  }
}
</script>

<style scoped>
.resign-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #f5f7fa;
  padding: 12px;
  box-sizing: border-box;
}

.search-container {
  background: #fff;
  padding: 20px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  position: relative;
}

.search-form {
  position: relative;
}

.search-header {
  position: relative;
  padding-right: 200px;
  min-height: 40px;
}

.search-main {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  align-items: center;
}

.search-main .el-form-item {
  margin: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.search-main .el-form-item .el-form-item__content {
  width: 180px;
}

.search-actions {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  gap: 12px;
  height: 40px;
  align-items: center;
}

.search-filters {
  margin-top: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  align-items: center;
}

.search-filters .el-form-item {
  margin: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.search-filters .el-form-item .el-form-item__label {
  margin-right: 8px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
  min-width: 60px;
  text-align: right;
}

.table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.pagination-container {
  background: #fff;
  padding: 16px 20px;
  margin-top: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: right;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

.empty-data {
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
}

/* 响应式设计 */
@media screen and (max-width: 1200px) {
  .search-header {
    padding-right: 180px;
  }

  .search-main .el-form-item .el-form-item__content {
    width: 160px;
  }
}

@media screen and (max-width: 768px) {
  .search-header {
    padding-right: 140px;
  }

  .search-main {
    gap: 8px 16px;
  }

  .search-main .el-form-item .el-form-item__content {
    width: 140px;
  }
}

@media screen and (max-width: 480px) {
  .search-header {
    padding-right: 0;
    min-height: auto;
  }

  .search-actions {
    position: static;
    margin-top: 16px;
    justify-content: flex-start;
    height: auto;
  }

  .search-main {
    gap: 4px 8px;
  }

  .search-main .el-form-item .el-form-item__content {
    width: 120px;
  }
}
</style>
